<cfsavecontent variable="local.ticketPackageJS">
	<cfoutput>
	<script language="javascript">
		function validateTicketPackageForm() {
			$('##frmTicketPackage :submit').prop('disabled',true);
			$('##ev_ticket_err_div').html('').hide();
			var arrReq = [];
			if($('##ticketPackageName').val() == '') arrReq[arrReq.length] = 'Enter the name of this ticket package.<br/>';
			var ticketCount = $('##ticketCount').val();
			if(ticketCount == '') 
				arrReq[arrReq.length] = 'Enter the number of seats in this ticket package.<br/>';
			else if(isNaN(ticketCount) || Number(ticketCount) != parseInt(ticketCount) || ticketCount < 1) 
				arrReq[arrReq.length] = 'Number of Seats in Package needs to be 1 or more.<br/>';			

			if ($('##frmTicketPackage ##ticketPackageName').val().trim() != ''){
				var checkDuplicateTicketResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){ 
						$('##frmTicketPackage').submit();
					}
					else {
						arrReq[arrReq.length] = 'This Ticket Package Name is already in use for this Event. Provide a unique Ticket Package Name to correct the issue.<br/>';	
						$('##ev_ticket_err_div').html(arrReq.join('')).addClass('alert alert-danger').show();
						$('##ev_ticket_err_div').removeClass('d-none');
						$('##frmTicketPackage :submit').prop('disabled',false);
						$('html, body').animate({
							scrollTop: $('##ev_ticket_err_div').offset().top
						}, 1000);
						return false;
					}
				};


				var objParams = { 
					eID:$('##frmTicketPackage ##eID').val(),
					ticketID:$('##frmTicketPackage ##ticketid').val(),					
					registrationID:$('##frmTicketPackage ##registrationID').val(),					
					strType:'ticketPackage',
				 	ticketName:$('##frmTicketPackage ##ticketPackageName').val().trim(),
					ticketPackageID:$('##frmTicketPackage ##ticketPackageID').val()
				};
				TS_AJX('ADMINEVENT','validateTicketAndPackageName',objParams,checkDuplicateTicketResult,checkDuplicateTicketResult,10000,checkDuplicateTicketResult);

			}else{
				if(arrReq.length) {
					$('##ev_ticket_err_div').html(arrReq.join('')).addClass('alert alert-danger').show();
					$('##frmTicketPackage :submit').prop('disabled',false);
					$('html, body').animate({
						scrollTop: $('##ev_ticket_err_div').offset().top
					}, 1000);
					return false;
				}
			}
			
			return false;
		}
		$(function() {
			$(document).on('click','##btnSaveTicketPackagetrigger',function(){
				validateTicketPackageForm();
			});
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ticketPackageJS)#">

<cfoutput>
#application.objWebEditor.showEditorHeadScripts()#
<div id="ev_ticket_err_div" style="display:none;" class="mb-2"></div>
<form name="frmTicketPackage" id="frmTicketPackage" action="#local.formlink#" method="post">
<input type="hidden" name="ticketid" id="ticketid" value="#arguments.event.getValue('ticketid')#">
<input type="hidden" name="ticketPackageID" id="ticketPackageID" value="#arguments.event.getValue('ticketPackageID')#">
<input type="hidden" name="eID" id="eID" value="#arguments.event.getValue('eID')#">
<input type="hidden" name="registrationID" id="registrationID" value="#arguments.event.getValue('registrationID')#">

<div class="row mx-0">
	<div class="col">
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="ticketPackageName" id="ticketPackageName"  value="#arguments.event.getValue('ticketPackageName')#"  class="form-control" maxlength="100"/>
					<label for="ticketPackageName">Name of Ticket Package *</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<textarea name="ticketPackageDescription" id="ticketPackageDescription" class="form-control" rows="2">#arguments.event.getValue('ticketPackageDescription')#</textarea>
					<label for="ticketPackageDescription">Description</label>
				</div>
			</div>
		</div>
		<div class="form-row">
			<div class="col col-sm-8">
				<div class="form-label-group">
					<input type="text" name="ticketCount" id="ticketCount" value="#arguments.event.getValue('ticketCount')#" class="form-control" maxlength="8"> 
					<label for="ticketCount">Number of Seats in Package *</label>
				</div>
			</div>
			<div class="col col-sm-4 pt-2">
				(1 or more)
			</div>
		</div>
		<div class="form-row">
			<div class="col col-sm-8">
				<div class="form-label-group">
					<input type="text" name="inventory" id="inventory" value="#arguments.event.getValue('inventory')#" class="form-control" maxlength="8"> 
					<label for="inventory">Max Packages Available</label>
				</div> 
			</div>
			<div class="col col-sm-4 pt-2">
				(leave blank if there is no limit)
			</div>
		</div>
		<div class="form-row">
			<div class="col col-sm-8">
				<div class="form-label-group">
					<input type="text" name="maxPerRegistrant" id="maxPerRegistrant" value="#arguments.event.getValue('maxPerRegistrant')#" class="form-control" maxlength="8"> 
					<label for="maxPerRegistrant">Max Packages Allowed per Registrant</label>
				</div> 
			</div>
			<div class="col col-sm-4 pt-2">
				(leave blank if there is no limit)
			</div>
		</div>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="adminOnly" id="adminOnly" class="form-control">
						<option value="0" <cfif arguments.event.getValue('adminOnly') is not 1>selected</cfif>>This ticket package is available on front-end registration.</option>
						<option value="1" <cfif arguments.event.getValue('adminOnly') is 1>selected</cfif>>This ticket package is NOT available on front-end registration.</option>
					</select>
					<label for="adminOnly">Ticket Package Availability *<</label>
				</div>
			</div>
		</div>		

		<div class="form-group row">
			<label for="ticketPackageConfirmationEmailInfo" class="col-sm-12 col-form-label-sm font-size-md">Information to Include in Confirmation Email</label>
		</div>
		<div class="form-group row">
			<div class="col-sm-12">
				#application.objWebEditor.embed(objname="ticketPackageConfirmationEmailInfo",objValue=arguments.event.getValue('ticketPackageConfirmationEmailInfo'), tools="ContentEditor")#
			</div>
		</div>
		<cfif arguments.event.getValue('ticketPackageID') gt 0 AND local.qryTicketPackageExcludedRates.recordCount>
			<div class="form-group row mt-2">
				<label class="col-12 col-form-label-sm font-size-md mb-0">This Ticket Package is Unavailable for following Rates</label>
				<div class="col-12 pl-3">
					<ul>
						<cfloop query="local.qryTicketPackageExcludedRates">
							<li>#local.qryTicketPackageExcludedRates.rateNameExpanded#</li>
						</cfloop>
					</ul>
				</div>
			</div>
		</cfif>
		<div class="form-group row mt-2">
			<label class="col-12 col-form-label-sm font-size-md">Price and Availability</label>
			<div class="col-12">
				<cfset local.objTZ = CreateObject("component","model.system.platform.tsTimeZone")>
				<cfset local.regTimeZoneName = local.objTZ.getTZFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'))>
				<cfset local.regTimeZone = local.objTZ.getTZCodeFromTZID(timeZoneID=arguments.event.getValue('mc_siteinfo.defaultTimeZoneID'))>
				<cfloop query="local.qryEventScheduleMappedPrice">
					<cfset local.thisScheduleAmt = NumberFormat(val(local.qryEventScheduleMappedPrice.amount),"0.00")>
					<div class="row mb-1">
						<div class="col-auto d-flex align-items-center pr-1">
							<input type="checkbox" name="amountCheck_#local.qryEventScheduleMappedPrice.scheduleID#" id="amountCheck_#local.qryEventScheduleMappedPrice.scheduleID#" value="1" <cfif len(local.qryEventScheduleMappedPrice.amount)>checked="checked"</cfif>>							
						</div>
						<div class="col-12 col-md-auto d-flex align-items-center px-md-1">
							<div class="input-group input-group-sm">
								<div class="input-group-prepend">
									<span class="input-group-text">$</span>
								</div>
								<input type="text" name="amount_#local.qryEventScheduleMappedPrice.scheduleID#" id="amount_#local.qryEventScheduleMappedPrice.scheduleID#" value="#local.thisScheduleAmt#" class="form-control form-control-sm" style="width:100px;" onBlur="this.value=formatCurrency(this.value);">
								<cfif arguments.event.getValue('mc_siteinfo.showCurrencyType') is 1><span class="ml-1">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span></cfif>
							</div>
						</div>
						<div class="col-12 col-md d-flex align-items-center pl-md-1">
							<label class="form-check-label" for="amountCheck_#local.qryEventScheduleMappedPrice.scheduleID#">
								#local.qryEventScheduleMappedPrice.rangeName# - #DateTimeFormat(local.objTZ.convertTimeZone(dateToConvert=local.qryEventScheduleMappedPrice.startDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone), 'm/d/yyyy hh:nn tt', local.regTimeZone)#
								to #DateTimeFormat(local.objTZ.convertTimeZone(dateToConvert=local.qryEventScheduleMappedPrice.endDate, fromTimeZone='US/Central', toTimeZone=local.regTimeZone), 'm/d/yyyy hh:nn tt', local.regTimeZone)#
							</label>
						</div>
					</div>
				</cfloop>
			</div>
		</div>
		<div class="form-group row mt-2 d-none">
			<div class="col text-right">
				<button name="btnSaveTicketPackage" id="btnSaveTicketPackage" type="submit" class="btn btn-sm btn-primary">Save Ticket Package</button>
				<button name="btnSaveTicketPackagetrigger" id="btnSaveTicketPackagetrigger" type="button" class="btn btn-sm btn-primary">Save Ticket Package</button>
			</div>
		</div>
	</div>
</div>
</form>
</cfoutput>