<cfcomponent output="false" extends="model.scheduledTasks.scheduledTasks">
	
	<cffunction name="runTask" access="public" returntype="void" output="false">
		<cfargument name="strTask" type="struct" required="true">
		<cfargument name="rc" type="struct" required="true">
		
		<cfset var local = structnew()>
		<cfset var strRepos = {}>
		<cfset var stDistinctLabels = "">
		<cfset var strDistinctLabels = {}>

		<!--- get the repos --->
		<cfset strRepos = getRepositories()>

		<!--- get the existing labels --->
		<cfset structEvery(strRepos, function(string repo) { 
			strRepos[arguments.repo] = getLabels(repo=arguments.repo);
			return true;
			}, true, 10)>

		<!--- get distinct list of labels --->
		<cfset structEvery(strRepos, function(string repo, struct strLabels) { 
			stDistinctLabels = listAppend(stDistinctLabels,structKeyList(arguments.strLabels,chr(7)),chr(7));
			return true;
			}, false)>
		<cfset listEvery(stDistinctLabels, function(string label) { 
			structInsert(strDistinctLabels, arguments.label, "", true);
			return true;
			}, chr(7), false, false, false)>

		<!--- every repo should have every label. ensure standard label colors are used as well. --->
		<cfset structEvery(strRepos, function(string repo, struct strLabels) { 
			syncLabels(repo=arguments.repo, strLabels=arguments.strLabels, strDistinctLabels=strDistinctLabels);
			return true;
			}, true, 10)>

		<cfset local.historyID = (arguments.strTask.historyID ?: 0)>
		<cfset addBatchIdentifierToHistory(historyID=local.historyID, batchIdentifier="", itemCount=listLen(structKeyList(strRepos)))>
	</cffunction>

	<cffunction name="getRepositories" access="private" output="false" returntype="struct">
		<cfset var local = structNew()>
		<cfset local.strFinalRepos = {}>

		<!--- this is hardcoded to a set of known repos we want to sync --->
		<cfset local.strKnownRepos = { 
			"Backend"="", 
			"CloudflareCustomPages"="",
			"ConsoleDevelopment"="", 
			"dockerServices"="", 
			"developerSetup"="",
			"depositionanalyzer"="", 
			"Eclips"="", 
			"Halon"="", 
			"JavascriptPackages"="",
			"kubernetesServices"="",
			"MC"="", 
			"MCProxy"="", 
			"membercentralAPI"="", 
			"NodeLambdaFunctions"="", 
			"Operations"="", 
			"paymentUtilities"="",
			"sqlclr"="", 
			"templateProcessor"="", 
			"trialsmithAI"="", 
			"trialsmith-agent-toolkit"="", 
			"TSAdmin"=""
		}>

		<cfset local.pageNum = 1>
		<cfset local.isDone = 0>
		<cfset local.apiBaseURL = "https://api.github.com/orgs/TrialSmith/repos">

		<cfloop condition="local.isDone is 0">
			<cfhttp url="#local.apiBaseURL#?page=#local.pageNum#" method="get" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
				 <cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
			</cfhttp>

			<cfset local.arrRepos = deserializeJSON(local.apiResult.fileContent)>

			<cfloop array="#local.arrRepos#" index="local.thisRepo">
				<cfif structKeyExists(local.strKnownRepos,local.thisRepo.name)>
					<cfset structInsert(local.strFinalRepos, local.thisRepo.name, arrayNew(1))>
				</cfif>
			</cfloop>

			<cfif isDefined("local.apiResult.responseheader.Link") and findNoCase('rel="next"',local.apiResult.responseheader.Link)>
				<cfset local.pageNum = local.pageNum + 1>
			<cfelse>
				<cfset local.isDone = 1>
			</cfif>
		</cfloop>

		<cfreturn local.strFinalRepos>
	</cffunction>
	
	<cffunction name="getLabels" access="private" output="false" returntype="struct">
		<cfargument name="repo" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.strFinalLabels = {}>

		<cfset local.pageNum = 1>
		<cfset local.isDone = 0>
		<cfset local.apiBaseURL = "https://api.github.com/repos/TrialSmith/#arguments.repo#/labels">

		<cfloop condition="local.isDone is 0">
			<cfhttp url="#local.apiBaseURL#?page=#local.pageNum#" method="get" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
				 <cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
			</cfhttp>

			<cfset local.arrLabels = deserializeJSON(local.apiResult.fileContent)>

			<cfloop array="#local.arrLabels#" index="local.thisLabel">
				<cfset structInsert(local.strFinalLabels, local.thisLabel.name, local.thisLabel)>
			</cfloop>

			<cfif isDefined("local.apiResult.responseheader.Link") and findNoCase('rel="next"',local.apiResult.responseheader.Link)>
				<cfset local.pageNum = local.pageNum + 1>
			<cfelse>
				<cfset local.isDone = 1>
			</cfif>
		</cfloop>

		<cfreturn local.strFinalLabels>
	</cffunction>

	<cffunction name="syncLabels" access="private" output="false" returntype="void">
		<cfargument name="repo" type="string" required="true">
		<cfargument name="strLabels" type="struct" required="true">
		<cfargument name="strDistinctLabels" type="struct" required="true">

		<cfset var local = structNew()>

		<!--- if the label exists and the color is what it should be, then ignore it --->
		<!--- if the label exists and the color doesnt match, update it --->
		<!--- if the label does not exist, add it --->

		<cfset local.strLabelColors = {}>
		<cfset local.strLabelColor["RN"] = "fef2c0">
		<cfset local.strLabelColor["HRN"] = "b4e878">
		<cfset local.strLabelColor["ON"] = "d4c5f9">
		<cfset local.strLabelColor["PRRR"] = "0e8a16">
		<cfset local.strLabelColor["PRNR"] = "d93f0b">
		<cfset local.strLabelColor["PRIR"] = "c5def5">
		<cfset local.strLabelColor["PRAH"] = "f9d0c4">
		<cfset local.strLabelColor["X"] = "fbca04">

		<cfloop collection="#arguments.strDistinctLabels#" item="local.thisLabel">
			<cfif left(local.thisLabel,3) eq "RN:">
				<cfset local.labelColor = local.strLabelColor.RN>
			<cfelseif left(local.thisLabel,4) eq "HRN:">
				<cfset local.labelColor = local.strLabelColor.HRN>
			<cfelseif left(local.thisLabel,3) eq "On ">
				<cfset local.labelColor = local.strLabelColor.ON>
			<cfelseif local.thisLabel eq "PR: Review Requested">
				<cfset local.labelColor = local.strLabelColor.PRRR>
			<cfelseif local.thisLabel eq "PR: Not Ready for Review">
				<cfset local.labelColor = local.strLabelColor.PRNR>
			<cfelseif local.thisLabel eq "PR: In Review">
				<cfset local.labelColor = local.strLabelColor.PRIR>
			<cfelseif local.thisLabel eq "PR: Approved but Held">
				<cfset local.labelColor = local.strLabelColor.PRAH>
			<cfelse>
				<cfset local.labelColor = local.strLabelColor.X>
			</cfif>
			<cfset local.apiRequestBody = "{ ""name"":""#local.thisLabel#"", ""description"":"""", ""color"":""#local.labelColor#"" }">

			<cfif structKeyExists(arguments.strLabels, local.thisLabel)>
				<cfif arguments.strLabels[local.thisLabel].color neq local.labelColor>
					<cfhttp url="https://api.github.com/repos/TrialSmith/#arguments.repo#/labels/#local.thisLabel#" method="PATCH" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
						 <cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
						 <cfhttpparam type="body" value="#local.apiRequestBody#">
					</cfhttp>
				</cfif>
			<cfelse>	
				<cfhttp url="https://api.github.com/repos/TrialSmith/#arguments.repo#/labels" method="post" useragent="MemberCentral.com" result="local.apiResult" charset="utf-8" throwonerror="true">
					 <cfhttpparam type="header" name="Authorization" value="token #application.strPlatformAPIKeys.github.token#">
					 <cfhttpparam type="body" value="#local.apiRequestBody#">
				</cfhttp>
			</cfif>
		</cfloop>
	</cffunction>

</cfcomponent>