<cfsavecontent variable="local.customJS">
	<cfoutput>
	#local.strTickets.js#

	<script type="text/javascript">
		function editTicket(t){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (t > 0 ? 'Edit' : 'Add') + ' Ticket',
				iframe: true,
				contenturl: '#this.link.editTicket#&eID=#arguments.event.getValue('eID')#&registrationID=#local.strEvent.qryEventRegMeta.registrationID#&ticketID=' + t,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmTicket button.submitBtn").click',
					extrabuttonlabel: 'Save Ticket'
				}
			});
		}
		function editTicketPackage(t,p){
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (p > 0 ? 'Edit' : 'Add') + ' Ticket Package',
				iframe: true,
				contenturl: '#this.link.editTicketPackage#&eID=#arguments.event.getValue('eID')#&registrationID=#local.strEvent.qryEventRegMeta.registrationID#&ticketID=' + t + '&ticketPackageID=' + p,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSaveTicketPackagetrigger").click',
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttonlabel: 'Save Ticket Package',
				}
			});
		}
		function removeTicket(t) {
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Remove Ticket',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this ticket?</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Remove Ticket',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				doRemoveTicket(t);
			});
		}
		function doRemoveTicket(t) {
			var removeTicketResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					MCModalUtils.hideModal();
					$('.MCTicketGrid'+t).remove();
					resetTicketUpDownLinks();
				} else {
					alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to remove this ticket.');
					$('##btnMCModalSave').prop('disabled',false).html('Remove Ticket');
				}
			};
			$('##btnMCModalSave').prop('disabled',true).html('Removing...');
			var objParams = { ticketID:t, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteTicket',objParams,removeTicketResult,removeTicketResult,30000,removeTicketResult);
		}
		function removeTicketPackage(p,t) {
			MCModalUtils.showModal({
				verticallycentered: true,
				size: 'md',
				title: 'Remove Ticket Package',
				strmodalbody: {
					content: '<div class="alert d-flex align-items-center pl-2 align-content-center alert-warning" role="alert"><span class="font-size-lg d-block d-40 mr-2 text-center"><i class="fa-regular fa-circle-question"></i></span><span>Are you sure you want to remove this ticket package?</span></div>',
				},
				strmodalfooter : {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-outline-danger',
					extrabuttonlabel: 'Remove Ticket Package',
				}
			});

			$('##btnMCModalSave').on('click', function(){
				doRemoveTicketPackage(p,t);
			});
		}
		function doRemoveTicketPackage(p,t) {
			var removeTicketPackageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					MCModalUtils.hideModal();
					$('##MCTicketPackage'+p).remove();
					resetPackageUpDownLinks(t);
				} else {
					alert(r.errmsg && r.errmsg.length ? r.errmsg : 'We were unable to remove this ticket package.');
					$('##btnMCModalSave').prop('disabled',false).html('Remove Ticket Package');
				}
			};
			$('##btnMCModalSave').prop('disabled',true).html('Removing...');
			var objParams = { ticketPackageID:p, eventSRID:#this.siteResourceID# };
			TS_AJX('ADMINEVENT','deleteTicketPackage',objParams,removeTicketPackageResult,removeTicketPackageResult,30000,removeTicketPackageResult);
		}
		function updateTicketTitle(tid,dtl,tname) {
			$('.MCTicketName'+tid).html(tname);
			$('.MCTicketName'+tid+'Inv').html(dtl);
			MCModalUtils.hideModal();
		}
		function updateTicketPackageTitle(pid,dtl,pname) {
			$('.MCTicketPackageName'+pid).html(pname);
			$('.MCTicketPackageName'+pid+'Inv').html(dtl);
			MCModalUtils.hideModal();
		}
		function injectTicketGrid(tid) {
			MCModalUtils.hideModal();

			if ($('##mccf_div_grids#local.strTickets.idExt# .MCTicketParent').length)
				$('##mccf_div_grids#local.strTickets.idExt# .MCTicketParent:last').after('<div id="divmct'+tid+'load"></div>');
			else 
				$('##mccf_div_grids#local.strTickets.idExt#').append('<div id="divmct'+tid+'load"></div>');

			$('html, body').animate({
				scrollTop: $('##divmct'+tid+'load').offset().top - 40
			}, 750);

			var fd = new Object();
				fd.csrid = #this.eventAdminSiteResourceID#;
				fd.usageRT = 'Event';
				fd.usageAN = 'ticket';
				fd.detailID = tid;

			$('##divmct'+tid+'load')
				.html('<div class="c"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i><br/><b>Please Wait...</b><br/>We\'re creating the ticket.</div>')
				.load('#this.link.loadResourceGrid#', fd, function() { $('##MCTicketParent'+tid).unwrap(); resetTicketUpDownLinks(); });
		}
		function injectTicketPackageGrid(tid,tpid) {
			MCModalUtils.hideModal();

			$('##MCTicketParent'+tid+' .card-body:first').append('<div id="divmctp'+tpid+'load"></div>');
			$('html, body').animate({
				scrollTop: $('##divmctp'+tpid+'load').offset().top - 40
			}, 750);

			var fd = new Object();
				fd.csrid = #this.eventAdminSiteResourceID#;
				fd.usageRT = 'Event';
				fd.usageAN = 'ticketPackage';
				fd.detailID = tpid;

			$('##divmctp'+tpid+'load')
				.html('<div class="c"><i class="fa-light fa-circle-notch fa-spin fa-2x"></i><br/><b>Please Wait...</b><br/>We\'re creating the ticket package.</div>')
				.load('#this.link.loadResourceGrid#', fd, function() { $('##MCTicketPackage'+tpid).unwrap(); resetPackageUpDownLinks(tid); });
		}
		function moveTicket(id,dir) {
			var moveTicketResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){ 
					if(dir=='up')
						resetTicketUp(id); 
					else
						resetTicketDown(id); 
				}
				else {
					alert('We were unable to move this ticket.');
				}
			};
			if(dir=='up'){
				if ($('##moveUpTicket'+id).hasClass('noActionEvent')) return false;
			} else {
				if ($('##moveDownTicket'+id).hasClass('noActionEvent')) return false;
			}
			var objParams = { ticketid:id, registrationID:#local.strEvent.qryEventRegMeta.registrationID#, dir:dir };
			TS_AJX('ADMINEVENT','doTicketMove',objParams,moveTicketResult,moveTicketResult,10000,moveTicketResult);
		}
		
		function moveTicketPackage(t,p,dir) {
			var moveTicketPackageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){ 
					if(dir=='up')
						resetTicketPackageUp(t,p);
					else
						resetTicketPackageDown(t,p);		 
				}
				else {
					alert('We were unable to move this ticket package.');
				}
			};
			if(dir=='up'){
				if ($('##moveUpTicketPackage'+p).hasClass('noActionEvent')) return false;
			} else {
				if ($('##moveDownTicketPackage'+p).hasClass('noActionEvent')) return false;
			}
				
			var objParams = { ticketPackageID:p, ticketID:t, dir:dir };
			TS_AJX('ADMINEVENT','doTicketPackageMove',objParams,moveTicketPackageResult,moveTicketPackageResult,10000,moveTicketPackageResult);
		}
		
		function resetTicketUp(id) {
			var prevElem = $('##MCTicketParent'+id).prevAll('.MCTicketParent').first();
			if (prevElem.length) {
				var prevElemID = prevElem.attr('id');
				$('##MCTicketParent'+id).insertBefore('##'+prevElemID);
				resetTicketUpDownLinks();

				$('html, body').animate({
					scrollTop:$('.MCTicketGrid'+id+':first').offset().top - 150
				}, 500);
			}
		}
		function resetTicketDown(id) {
			var nextElem = $('##MCTicketParent'+id).nextAll('.MCTicketParent').first();
			if (nextElem.length) {
				var nextElemID = nextElem.attr('id');
				$('##MCTicketParent'+id).insertAfter('##'+nextElemID);
				resetTicketUpDownLinks();

				$('html, body').animate({
					scrollTop:$('.MCTicketGrid'+id+':first').offset().top - 150
				}, 500);
			}
		}
		function resetTicketPackageUp(t,p) {
			var prevElem = $('##MCTicketPackage'+p).prevAll('.MCTicketPackageGrid').first();
			if (prevElem.length) {
				var prevElemID = prevElem.attr('id');
				$('##MCTicketPackage'+p).insertBefore('##'+prevElemID);
				resetPackageUpDownLinks(t);
			}
		}
		function resetTicketPackageDown(t,p) {
			var nextElem = $('##MCTicketPackage'+p).nextAll('.MCTicketPackageGrid').first();
			if (nextElem.length) {
				var nextElemID = nextElem.attr('id');
				$('##MCTicketPackage'+p).insertAfter('##'+nextElemID);
				resetPackageUpDownLinks(t);
			}
		}
		function resetPackageUpDownLinks(tid) {
			var packageElements = $('.MCTicketGrid'+tid).not('[id="MCTicketParent'+tid+'"]');
			var totalPackages = packageElements.length;

			if(totalPackages > 0) {
				$.each(packageElements, function(index, element) {
					var thisElemID = $(this).attr('id');
					var thisElemTPID = Number(thisElemID.replace('MCTicketPackage',''));

					var thisElemCanMoveUp = !$(this).find('##moveUpTicketPackage'+thisElemTPID).hasClass('noActionEvent');
					var thisElemCanMoveDown = !$(this).find('##moveDownTicketPackage'+thisElemTPID).hasClass('noActionEvent');

					if(index == 0 && thisElemCanMoveUp) {
						$(this).find('##moveUpTicketPackage'+thisElemTPID).addClass('noActionEvent');
					} else if (index > 0 && !thisElemCanMoveUp) {
						$(this).find('##moveUpTicketPackage'+thisElemTPID).removeClass('noActionEvent');
					}

					if(index + 1 == totalPackages && thisElemCanMoveDown) {
						$(this).find('##moveDownTicketPackage'+thisElemTPID).addClass('noActionEvent');
					} else if (index + 1 < totalPackages && !thisElemCanMoveDown) {
						$(this).find('##moveDownTicketPackage'+thisElemTPID).removeClass('noActionEvent');
					}
				});
			}
		}
		function resetTicketUpDownLinks() {
			var ticketElements = $('.MCTicketParent');
			var totalTickets = ticketElements.length;

			if(totalTickets > 0) {
				$.each(ticketElements, function(index, element) {
					var thisElemID = $(this).attr('id');
					var thisElemTID = Number(thisElemID.replace('MCTicketParent',''));

					var thisElemCanMoveUp = !$(this).find('##moveUpTicket'+thisElemTID).hasClass('noActionEvent');
					var thisElemCanMoveDown = !$(this).find('##moveDownTicket'+thisElemTID).hasClass('noActionEvent');

					if(index == 0 && thisElemCanMoveUp) {
						$(this).find('##moveUpTicket'+thisElemTID).addClass('noActionEvent');
					} else if (index > 0 && !thisElemCanMoveUp) {
						$(this).find('##moveUpTicket'+thisElemTID).removeClass('noActionEvent');
					}

					if(index + 1 == totalTickets && thisElemCanMoveDown) {
						$(this).find('##moveDownTicket'+thisElemTID).addClass('noActionEvent');
					} else if (index + 1 < totalTickets && !thisElemCanMoveDown) {
						$(this).find('##moveDownTicket'+thisElemTID).removeClass('noActionEvent');
					}
				});
			}
		}
		function closeBox() { MCModalUtils.hideModal(); }
	</script>
	<style type="text/css">
		.noActionEvent { opacity:0.6; }
	</style>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.customJS#">

<cfoutput>
<div id="mccf_div_custom#local.strTickets.idExt#">
	<h4>Tickets and Ticket Packages</h4>
	<div class="row">
		<div class="col-12">Tickets are optional for special events and appear on a screen after Event Rate selection. Registrants are shown tickets that are included in their rate (if any) and options for adding more for guests, individually or as a bundle in a package.</div>		
		<div class="col-12 text-right pt-1">
			<button type="button" class="btn btn-sm btn-primary" onclick="editTicket(0);">
				<span class="btn-wrapper--icon"><i class="fa-regular fa-ticket-simple"></i></span>
				<span class="btn-wrapper--label">Add Ticket</span>
			</button>
		</div>
	</div>	
</div>
<div class="mt-1 mb-3">
	#local.strTickets.html#
</div>
</cfoutput>