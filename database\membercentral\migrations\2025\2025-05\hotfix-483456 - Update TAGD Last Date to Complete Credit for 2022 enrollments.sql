use seminarWeb
GO

/*
select e.enrollmentID, e.dateEnrolled, e.dateCompleted, eac.lastDateToComplete, dateadd(ms,-3,cast(dateadd(day,46,cast(e.dateEnrolled as date)) as datetime)) as shouldBeLastDateToComplete 
from dbo.tblEnrollments as e
inner join dbo.tblEnrollmentsAndCredit as eac on eac.enrollmentID = e.enrollmentID
where e.seminarID in (13522,14191)
and e.dateEnrolled < '3/1/2023'
and e.dateCompleted is null
and eac.lastDateToComplete > dateadd(day,46,e.dateEnrolled)
and e.isactive = 1
order by e.dateEnrolled
*/

UPDATE eac
set eac.lastDateToComplete = dateadd(ms,-3,cast(dateadd(day,46,cast(e.dateEnrolled as date)) as datetime))
from dbo.tblEnrollments as e
inner join dbo.tblEnrollmentsAndCredit as eac on eac.enrollmentID = e.enrollmentID
where e.seminarID in (13522,14191)
and e.dateEnrolled < '3/1/2023'
and e.dateCompleted is null
and eac.lastDateToComplete > dateadd(day,46,e.dateEnrolled)
and e.isactive = 1;
GO

