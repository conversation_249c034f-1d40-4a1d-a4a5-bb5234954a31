<cfsavecontent variable="local.limitScheduleJS">
	<cfoutput>
	#local.strOverflowGLAcctWidget.js#
	<script language="javascript">
		let GLAccountLimitTable;
		function saveLimitSchedule() {
			var arrReq = new Array();
			var scheduleID = $('##scheduleID').val();
			mca_hideAlert('err_limitschedule');
			
			if($('##limitName').val().trim() == '') arrReq[arrReq.length] = 'Enter the limit name.';
			if($('##startDate').val() == '') arrReq[arrReq.length] = 'Enter the limit start date.';
			if($('##endDate').val() == '') arrReq[arrReq.length] = 'Enter the limit end date.';
			if($('##overflowGLAccountID').val() == 0) arrReq[arrReq.length] = 'Enter the overflow GL Account.';

			if(arrReq.length){
				mca_showAlert('err_limitschedule', arrReq.join('<br/>'));
				return false;
			}

			$('##divLimitScheduleForm').addClass('d-none');
			$('##saveLoadingDIV').removeClass('d-none');

			var saveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					top.reloadLimitsSchedules();
					top.MCModalUtils.hideModal();
					if (scheduleID == 0){ top.$('.modal-backdrop').remove();top.editLimitSchedule(r.scheduleid);}
				} else if (r.success && r.success.toLowerCase() == 'false') {
					var jserr = '';
					var emsg = $.parseXML(r.xmlvalidationresult);
					$(emsg).find('e').each(function() {
						jserr += $(this).attr('msg') + '<br/>';
					});
					mca_showAlert('err_limitschedule', jserr);
					$('##divLimitScheduleForm').removeClass('d-none');
					$('##saveLoadingDIV').addClass('d-none');
				} else {
					alert('We ran into an error while saving the limit schedule. Try again.');
				}
			};

			var objParams = { scheduleID:scheduleID, limitName:$('##limitName').val().trim(),
				startDate:$('##startDate').val(), endDate:$('##endDate').val(), overflowGLAccountID:$('##overflowGLAccountID').val() };
			TS_AJX('ADMINGLACCT','saveLimitSchedule',objParams,saveResult,saveResult,20000,saveResult);
		}
		function editGLAccountLimit(sid,lid) {
			mca_hideAlert('err_limitschedule');
			
			var loadURL = '#local.editGLAccountLimitLink#&scheduleID='+sid+'&limitID='+lid;

			$('div##addGLAccountLimit').html('<div class="my-3 text-center"><div class="spinner-border" role="status"></div><div class="font-weight-bold mt-2">Loading...</div></div>')
				.show()
				.load(loadURL);
		}
		function moveGLAccountLimit(rid,dir) {
			var moveResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					var tr = $('##row_'+rid);
					if(dir == 'up'){
						var trprev = tr.prev();
						tr.remove().insertBefore(trprev);
					}
					else {
						var trnext = tr.next();
						tr.remove().insertAfter(trnext);
					}
					resetActionIcons();
				}
			};
			var objParams = { limitID:rid, dir:dir };
			TS_AJX('ADMINGLACCT','doGLAccountLimitMove',objParams,moveResult,moveResult,10000,moveResult);
		}
		function resetActionIcons() {
				$('##GLAccountLimitTable tbody tr td a.limitmoveup, ##GLAccountLimitTable tbody tr td a.limitmovedown').addClass('invisible');
				if ($('##GLAccountLimitTable tbody tr').length > 1) {
					$('##GLAccountLimitTable tbody tr td').find('a.limitmoveup').removeClass('invisible');
					$('##GLAccountLimitTable tbody tr td').find('a.limitmovedown').removeClass('invisible');
					$('##GLAccountLimitTable tbody tr:first').find('a.limitmoveup').addClass('invisible');
					$('##GLAccountLimitTable tbody tr:last').find('a.limitmovedown').addClass('invisible');
				}
			}
		function reloadPage() { GLAccountLimitTable.draw(); }
		function deleteGLAccountLimit(id) {
			var deleteResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					GLAccountLimitTable.draw();
				} else {
					alert('We ran into an error while deleting the gl account limit. Try again.');
					delBtn.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
				}
			};

			let delBtn = $('##btnDel'+id);
			mca_initConfirmButton(delBtn, function(){
				var objParams = { limitID:id };
				TS_AJX('ADMINGLACCT','deleteGLAccountLimit',objParams,deleteResult,deleteResult,10000,deleteResult);
			});
		}

		$(function(){
			<cfif local.scheduleID gt 0>
				GLAccountLimitTable = $('##GLAccountLimitTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info":false,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.limitGLAcctListLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "thePathExpanded","width": "65%" },
					{ "data": "maxAmount","width": "15%" },
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-sm text-primary p-1 mx-1" title="Edit Limit GL" onclick="editGLAccountLimit('+data.scheduleID+','+data.limitID+');return false;"><i class="fa-solid fa-pencil"></i></a>';						
								renderData += '<a href="##" id="btnDel'+data.limitID+'" class="btn btn-sm text-danger p-1 mx-1 removePayProf" title="Remove Limit GL" onclick="deleteGLAccountLimit('+data.limitID+');return false;"><i class="fa-solid fa-trash-can"></i></a>';								
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 limitmoveup'+(!data.canmoveup ? ' invisible' : '')+'" title="Move GL Account Limit Up" onclick="moveGLAccountLimit('+data.limitID+',\'up\');return false;"><i class="fa-solid fa-arrow-up"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1 limitmovedown'+(!data.canmovedown ? ' invisible' : '')+'" title="Move GL Account Limit Down" onclick="moveGLAccountLimit('+data.limitID+',\'down\');return false;"><i class="fa-solid fa-arrow-down"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "20%",
						"className": "text-center"
					}
				],
				"searching": false,
				"ordering": false
			});
			</cfif>

			mca_setupDatePickerRangeFields('startDate','endDate');
			mca_setupCalendarIcons('frmLimitSchedule');

			$('##frmLimitSchedule input').keyup(function(event) {
				if (event.keyCode == 13) {
					saveLimitSchedule();
					return false;
				}
			});

			<cfif local.scheduleID is 0>
				top.$('##MCModalLabel').html('Add Limit Schedule');
			<cfelse>
				top.$('##MCModalLabel').html('Edit Limit Schedule');
			</cfif>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.limitScheduleJS#">

<cfoutput>
<div id="divLimitScheduleForm">

	<div id="err_limitschedule" class="alert alert-danger mb-2 d-none"></div>

	<form name="frmLimitSchedule" id="frmLimitSchedule" class="px-3">
		<input type="hidden" name="scheduleID" id="scheduleID" value="#local.scheduleID#">

		<div class="form-row mt-2">
			<div class="col">
				<div class="form-label-group">
					<input type="text" name="limitName" id="limitName" value="#local.qryLimitSchedule.limitName#"  class="form-control">
					<label for="limitName">Limit Name <span class="text-danger">*</span></label>
				</div>
			</div>
		</div>
		<div class="form-group">
			<div class="form-label-group">
				<div class="input-group dateFieldHolder">
					<input type="text" name="startDate" id="startDate" value="#DateFormat(local.qryLimitSchedule.startDate,'m/d/yyyy')#" class="form-control dateControl">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="startDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
					<label for="startDate">Start Date <span class="text-danger">*</span></label>
				</div>
			</div>
		</div>
	
		<div class="form-group">
			<div class="form-label-group">
				<div class="input-group dateFieldHolder">
					<input type="text" name="endDate" id="endDate" value="#DateFormat(local.qryLimitSchedule.endDate,'m/d/yyyy')#" class="form-control dateControl">
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="endDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
					<label for="endDate">End Date <span class="text-danger">*</span></label>
				</div>
			</div>
		</div>
		<div class="form-group">
			#local.strOverflowGLAcctWidget.html#
		</div>

		<cfif local.scheduleID gt 0>
			<div class="row align-items-end mt-5">
				<div class="col"><h5 class="mb-0">GL Accounts with Limits</h5></div>
				<div class="col-auto text-right">
					<button type="button" class="btn btn-sm btn-secondary" onclick="editGLAccountLimit(#local.scheduleID#,0);">
						<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
						<span class="btn-wrapper--label">Add Limit</span>
					</button>
				</div>
			</div>
			<div id="addGLAccountLimit"> </div>
			<table id="GLAccountLimitTable" class="table table-sm table-striped table-bordered" style="width:100%">
				<thead>
					<tr>
						<th>GL Account</th>
						<th>Amount</th>
						<th>Actions</th>
					</tr>
				</thead>
			</table>			
		</cfif>
		<div class="form-group row mt-2 d-none">
			<div class="offset-sm-3 col-sm-9">
				<button type="button" id="btnSaveSchedule" name="btnSaveSchedule" class="btn btn-sm btn-primary" onclick="saveLimitSchedule();">Save</button>
			</div>
		</div>
	</form>
</div>

<div id="saveLoadingDIV" class="d-none">
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="font-weight-bold mt-2">Please wait while we validate and save the details.</div>
	</div>
</div>
</cfoutput>