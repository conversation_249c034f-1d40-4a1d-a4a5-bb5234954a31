<cfset local.selectedTab = event.getTrimValue("tab","current")>
<cfif event.getTrimValue("lockTab","false")>
	<cfset local.lockTab = local.selectedTab>
<cfelse>
	<cfset local.lockTab = "">
</cfif>

<cfsavecontent variable="local.ratesJS">
	<cfoutput>
	<script language="javascript">
		function validateRateForm() {
			var arrReq = new Array();
			hideAlert();
			
			var rateStart = $('##rateStartDate').val();
			var rateEnd = $('##rateEndDate').val();
			var termStart = $('##termStartDate').val();
			var termEnd = $('##termEndDate').val();

			if($('##rateName').val() == '') arrReq[arrReq.length] = "Enter the name of this rate";			
			if ((rateStart.length == 0) || (rateEnd.length == 0)) arrReq[arrReq.length] = 'You must enter dates for Rate Available.';
			if ((termStart.length == 0) || (termEnd.length == 0)) arrReq[arrReq.length] = 'You must enter dates for Term Dates.';

			<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
				var recogStart = $('##recogStartDate').val();
				var recogEnd = $('##recogEndDate').val();
				if ((recogStart.length == 0) || (recogEnd.length == 0)) arrReq[arrReq.length] = 'You must enter dates for Recognition Dates.';
			</cfif>
			
			<cfloop query="local.qryRateFreqs">
				<cfif local.qryRateFreqs.rateRequired eq 1>
					if ($('##freqAmt_#local.qryRateFreqs.frequencyID#').val().length == 0 || $('##freqAmt_#local.qryRateFreqs.frequencyID#_blank').is(':checked')) {
						arrReq[arrReq.length] = "You must enter an amount for #local.qryRateFreqs.frequencyName#.";
					}
				</cfif>
			</cfloop>

			if(arrReq.length){
				showAlert(arrReq.join('<br/>'));
				return false;
			}

			let errMsg = validateProfileSelected();

			if($.trim(errMsg).length) {
				showAlert(errMsg);
				return false;
			}
			$('##frmRate').submit();
			top.$('##btnMCModalSave').prop('disabled', true);
			return true;
		}
		function chkFrontEndAllowChangePriceOptions() {
			if ($('##selFrontEndAllowChangePrice').val() == 1) $('##FrontEndAllowChangePriceOptions').removeClass('d-none');
			else $('##FrontEndAllowChangePriceOptions').addClass('d-none');
		}
		function chkFreqAmt(freqID) {
			$(".freqAmt").each(function(){
				if($.trim($(this).val()).length == 0){
					$(this).closest(".rateFrequencySection").find('.divRFMP').addClass('d-none');
					$(this).closest(".rateFrequencySection").find(".freqAmtBlank").attr('checked', true);
				}
				else {
					$(this).closest(".rateFrequencySection").find('.divRFMP').removeClass('d-none');
					$(this).closest(".rateFrequencySection").find(".freqAmtBlank").attr('checked', false);
				}
			});

			validateProfileSelected();
		}
		function chkFreqBlank(freqID) {
			if ($('##freqAmt_' + freqID + '_blank').is(':checked')) {
				$('##freqAmt_' + freqID).val('');
			}
			chkFreqAmt(freqID);
		}
		function validateProfileSelected() {
			var arrErrorMsg = new Array();
			$(".freqAmt").each(function(){
				let freqID = $(this).data('freqid');
				if($(this).val().length && !$('input[name="addRFMP_'+freqID+'"]:checked').length) {
					$(this).closest(".rateFrequencySection").find(".freqAmtMsg").removeClass('d-none');
					arrErrorMsg[arrErrorMsg.length] = $(this).closest(".rateFrequencySection").find(".freqAmtMsg").html();
				} else {
					$(this).closest(".rateFrequencySection").find(".freqAmtMsg").addClass('d-none');
				}
			});

			return arrErrorMsg.join('<br/>');
		}
		function hideAlert() { $('##err_rate').html('').addClass('d-none'); };
		function showAlert(msg) { $('##err_rate').html(msg).removeClass('d-none'); };
		
		$(function() {
			<cfif local.rateID GT 0>
				top.$('##MCModalHeader ##MCModalLabel').html('Edit Subscription Rate - #EncodeForJavaScript(local.qryRate.rateName)#');
			</cfif>
			mca_initNavPills('editRateTabs', '#local.selectedTab#', '#local.lockTab#');

			mca_setupDatePickerRangeFields('rateStartDate', 'rateEndDate', true);
			mca_setupDatePickerRangeFields('termStartDate', 'termEndDate', true);
			
			$('##termEndDate').change( function(e) { 
				$('##graceEndDate').datetimepicker('destroy');
				var rsd = $('##termEndDate').val();
				var red = $('##graceEndDate').val();
				if (rsd.length)	{
					if (red.length)	{ if (mca_getParsedDateTime(rsd) > mca_getParsedDateTime(red)) $('##graceEndDate').val(rsd); }
					else $('##graceEndDate').val(rsd);
				}
				mca_setupDatePickerField('graceEndDate',$('##termEndDate').val());
			} );
			mca_setupDatePickerField('graceEndDate',$('##termEndDate').val());

			<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
				mca_setupDatePickerRangeFields('recogStartDate','recogEndDate', true);
			</cfif>
			
			validateProfileSelected();
			mca_setupCalendarIcons('frmRate');
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ratesJS)#">

<cfoutput>
<div id="divRateForm">
	<form name="frmRate" id="frmRate" action="#local.formlink#" method="post" >
		<input type="hidden" name="rateid"  id="rateid" value="#local.rateID#">

		<div id="err_rate" class="alert alert-danger mb-2 d-none"></div>

		<ul class="nav nav-pills nav-pills-dotted" id="editRateTabs">
			<cfset local.thisTabName = "main">
			<cfset local.thisTabID = "rateMainTab">
			<li class="nav-item">
				<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
						aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Main</a>
			</li>

			<cfset local.thisTabName = "af">
			<cfset local.thisTabID = "rateAFTab">
			<li class="nav-item">
				<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" 
						aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Advance Formulas</a>
			</li>
		</ul>

		<div class="tab-content mc_tabcontent p-2 pb-0" id="pills-tabContent">
			<div class="tab-pane fade" id="pills-rateMainTab" role="tabpanel" aria-labelledby="rateMainTab">
				<cfinclude template="frm_ratesMain.cfm">
			</div>
			<div class="tab-pane fade" id="pills-rateAFTab" role="tabpanel" aria-labelledby="rateAFTab">
				<cfinclude template="frm_ratesAF.cfm">
			</div>
		</div>
		<button type="button" name="btnSubmit" id="btnSubmit" class="d-none" onClick="validateRateForm();"></button>
	</form>
</div>
</cfoutput>