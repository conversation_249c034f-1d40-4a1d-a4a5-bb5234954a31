$(document).ready(function(){
	$('.fa, .fas, .fa-brands, .fa-solid, .ti-home, .ti-email, .ti-mobile, .ti-shopping-cart, .ti-search, .ti-close, ti-user').each(function() {
		if ($.trim($(this).html()) === '&nbsp;') {
			$(this).html('');
		}
	});
	//Fixed Header Scroll Function Start
	$(window).scroll(function(){
		if($(window).scrollTop() > 50){
			$("#header").addClass("fixed-header", 40);	
			$(".banner-content-bottom").addClass("fixed", 40);	
			$("#header").addClass("shrink", 40);
		}else{
			$("#header").removeClass("fixed-header" , 40);
			$("#header").removeClass("shrink" , 40);
			$(".banner-content-bottom").removeClass("fixed", 40);	
		}	
		
		if($(window).scrollTop() > 115){
			$(".page-link-wrap").addClass("fixed", 50);	
		}else{
			$(".page-link-wrap").removeClass("fixed", 50);	
		}
	});
	//Fixed Header Scroll Function End

	// menu css
	$('.navMenu > ul > li:has(ul)').addClass('dropdown');
	$('.navMenu > ul > li:has(ul)').append( "<span class='dropdownArrow'></span>" );
	$('.navMenu > ul > li > ul').addClass('dropdown-menu');
	$('.navMenu > ul > li > ul > li:has(ul)').addClass('dropdown-submenu');
	$('.navMenu > ul > li > ul > li:has(ul)').append( "<span class='dropdownArrow'></span>" );
	$('.navMenu > ul > li > ul > li.dropdown-submenu > ul').addClass('subdropdown-menu'); 
	$('.navMenu > ul > li > ul > li > ul > li:has(ul)').addClass('dropdown-submenu');
	$('.navMenu > ul > li > ul > li > ul > li:has(ul)').append( "<span class='dropdownArrow'></span>" );
	$('.navMenu > ul > li > ul > li.dropdown-submenu > ul > li.dropdown-submenu > ul').addClass('subdropdown-menu'); 

	//  add remove class in header
	$('#navBtn').click(function() {
		$('.navbar-inner .menuDiv').toggleClass("show-menu");
		$('#navBtn').toggleClass("active");
	});


	$('.dropdown-submenu .dropdownArrow').on('click', function () {
	  $(this).parent().children('.subdropdown-menu').slideToggle();
	});

	$('.dropdown .dropdownArrow').on('click', function () {
	  $(this).parent().toggleClass('menu-open');
	  $(this).parent().find('.dropdown-menu').slideToggle();
	  $(this).parent().find('ul').find('ul').slideUp();
	  $(this).parent().find('ul').find('.dropdown-submenu.menu-open').removeClass('menu-open');
	  
	});


	//Accordian
  $('.accordion').each(function () {
    var $accordian = $(this);
    $accordian.find('.accordion-head').on('click', function () {
            $(this).parent().find(".accordion-head").removeClass('open closee');
            $(this).removeClass('open').addClass('closee');
            $accordian.find('.accordion-body').slideUp();
            $accordian.find('.accordion-body').toggleClass('openBody');
            if (!$(this).next().is(':visible')) {
                $(this).removeClass('closee').addClass('open');
                $(this).next().slideDown();
            }
        });
  });
    

    /*****************/
	  $(".quicklink-mobile .DiamondBullets").hide();
	  $(".quicklink-mobile h3").click(function(){
	      $(".quicklink-mobile .DiamondBullets").slideToggle();
	      $(this).toggleClass("quicklink-open");
	  });

  
	//$(".searchbar-top").hide();
	$(".search-icon").click(function() {
		$(".searchbar-top").slideToggle();
	});
	
	$(".sliderHolder.item ul").addClass("carousel-caption");
	$(".sliderHolder.item h1").addClass("TitleText");
	$(".sliderHolder.item a").addClass("WhiteBorder");
	$(".sliderHolder.item >ul").each(function(){
		$('li',this).eq(1).wrapInner("<p></p>");
	});
		
	var zoneD1String = "";

	$('.zoneD1Wrapper ul').each(function () {
		var title = $(this).find('li').eq(0).find('a');
		var date = $(this).find('li').eq(1).text();

		zoneD1String += `
			<div class="event-wrap">
				<h3 class="HeaderTextSmall"><a href="${title.attr('href')}">${title.text()}</a></h3>
				<p>
					<a href="${title.attr('href')}"><i class="fa-solid fa-calendar">&nbsp;</i> ${date}</a>
				</p>
			</div>
		`;
	});

	zoneD1String = `<h2 class="HeaderText right-double-line">${$('.zoneD1Wrapper > h2').text()}</h2>${zoneD1String}<div class="text-right"><a href="${$('.zoneD1Wrapper > a').attr('href')}" class="OliveBorder">${$('.zoneD1Wrapper > a').text()}</a></div>`;
	if($('.zoneD1Wrapper ul').length){
		$('.zoneD1Holder').replaceWith(`<div class="span4">${zoneD1String}</div>`);
	}
	var zoneE1String = "";

	$('.zoneE1Wrapper ul').each(function () {
		var title = $(this).find('li').eq(0).find('a');
		var date = $(this).find('li').eq(1).text();

		zoneE1String += `
			<div class="event-wrap">
				<h3 class="HeaderTextSmall"><a href="${title.attr('href')}">${title.text()}</a></h3>
				<p>
					<a href="${title.attr('href')}"><i class="fa-solid fa-calendar">&nbsp;</i> ${date}</a>
				</p>
			</div>
		`;
	});

	zoneE1String = `<h2 class="HeaderText right-double-line">${$('.zoneE1Wrapper > h2').text()}</h2>${zoneE1String}<div class="text-right"><a href="${$('.zoneE1Wrapper > a').attr('href')}" class="OliveBorder">${$('.zoneE1Wrapper > a').text()}</a></div>`;
	if($('.zoneE1Wrapper ul').length){
		$('.zoneE1Holder').replaceWith(`<div class="span4">${zoneE1String}</div>`);
	}

	var zoneF1String = "";

	$('.zoneF1Wrapper ul').each(function () {
		var title = $(this).find('li').eq(0).find('a');
		var date = $(this).find('li').eq(1).text();

		zoneF1String += `
			<div class="event-wrap">
				<h3 class="HeaderTextSmall"><a href="${title.attr('href')}">${title.text()}</a></h3>
				<p>
					<a href="${title.attr('href')}"><i class="fa-solid fa-calendar">&nbsp;</i> ${date}</a>
				</p>
			</div>
		`;
	});

	zoneF1String = `<h2 class="HeaderText right-double-line">${$('.zoneF1Wrapper > h2').text()}</h2>${zoneF1String}<div class="text-right"><a href="${$('.zoneF1Wrapper > a').attr('href')}" class="OliveBorder">${$('.zoneF1Wrapper > a').text()}</a></div>`;
	if($('.zoneF1Wrapper ul').length){
		$('.zoneF1Holder').replaceWith(`<div class="span4">${zoneF1String}</div>`);
	}
	
	var zoneG1String = "";
	$('.zoneG1Wrapper > ul > li').each(function () {
		var $items = $(this).find('ul > li');

		var imgSrc = $items.eq(0).find('img').attr('src') || '';
		var title = $items.eq(1).text().trim();
		var desc = $items.eq(2).text().trim();
		var $link = $items.eq(3).find('a');
		var linkText = $link.text().trim();
		var linkHref = $link.attr('href') || '#';

		zoneG1String += `
		  <div class="span4">
			<div class="event-wrap">
			  <div class="event-image">
				<a href="${linkHref}">
				  <img src="${imgSrc}" alt="" />
				</a>
			  </div>
			  <div class="event-content">
				<h3 class="HeaderTextSmall"><a href="${linkHref}">${title}</a></h3>
				<p>${desc}</p>
				<a href="${linkHref}" class="PurpleBorder">${linkText}</a>
			  </div>
			</div>
		  </div>
		`;
	});
	if($('.zoneG1Wrapper > ul > li').length){
		$('.zoneG1Holder').replaceWith(zoneG1String);
	}
	
	var zoneH1String = "";	
	$('.zoneH1Wrapper ul').each(function () {
		var $ul = $(this);
		var title = $ul.find('li').eq(0).text();
		var date = $ul.find('li').eq(1).text();
		var summary = $ul.find('li').eq(2).text();
		var link = $ul.find('li').eq(3).find('a').attr('href');
		var linkText = $ul.find('li').eq(3).find('a').text();
		
		var classType = $ul.find('li').eq(4).text().trim();
		var creditType = $ul.find('li').eq(5).text().trim();

		// Build optional content blocks ahead of time
		var classTypeHtml = classType ? `<p><i class="fa-solid fa-chalkboard">&nbsp;</i> Class Type: ${classType}</p>` : '';
		var creditTypeHtml = creditType ? `<p><i class="fa-solid fa-tags">&nbsp;</i> Credit Types: ${creditType}</p>` : '';

		zoneH1String += `
			<div class="span4">
				<div class="event-wrap">
					<h3 class="HeaderTextSmall"><a href="${link}">${title}</a></h3>
					<p><i class="fa-solid fa-calendar">&nbsp;</i> ${date}</p>
					<p>${summary}</p>
					${classTypeHtml}
					${creditTypeHtml}
					<a href="${link}" class="GreyBorder">${linkText}</a>
				</div>
			</div>`;

	});
	if($('.zoneH1Wrapper ul').length){
		$('.zoneH1Holder').replaceWith(zoneH1String);
	}
    
	var zoneI1String = "";
	var $items = $('.zoneI1Wrapper > ul > li');

	var bgImage = $items.eq(0).find('img').attr('src') || '';
	var headingText = $items.eq(1).text().trim();
	var $link = $items.eq(2).find('a');
	var linkText = $link.text().trim();
	var linkHref = $link.attr('href') || '#';

	zoneI1String += `
	  <div class="bg-image-sec bg-fixed-sticky" style="background-image: url('${bgImage}');">
		<div class="container containerCustom">
		  <div class="row">
			<div class="span2"></div>
			<div class="span8 text-center">
			  <h2 class="HeaderText text-center text-white">${headingText}</h2>
			  <a href="${linkHref}" class="WhiteBorder btn-sm">${linkText}</a>
			</div>
		  </div>
		</div>
	  </div>
	`;
	if($('.zoneI1Wrapper > ul > li').length){
		$('.zoneI1Holder').replaceWith(zoneI1String);
	}
	
	
	var zoneJ1String = "";

	$('.zoneJ1Wrapper > ul > li').each(function () {
		var $items = $(this).find('ul > li');

		var imgSrc = $items.eq(0).find('img').attr('src') || '';
		var title = $items.eq(1).text().trim();
		var desc = $items.eq(2).text().trim();
		var $link = $items.eq(3).find('a');
		var linkText = $link.text().trim();
		var linkHref = $link.attr('href') || '#';

		zoneJ1String += `
		  <div class="span4">
			<div class="event-wrap">
			  <div class="event-image">
				<a href="${linkHref}">
				  <img src="${imgSrc}" alt="" />
				</a>
			  </div>
			  <div class="event-content">
				<h3 class="HeaderTextSmall">${title}</h3>
				<p>${desc}</p>
				<a href="${linkHref}" class="PurpleBorder">${linkText}</a>
			  </div>
			</div>
		  </div>
		`;
	});
	if($('.zoneJ1Wrapper > ul > li').length){
		$('.zoneJ1Holder').replaceWith(zoneJ1String);
	}
	
	var zoneR1String = "";

	$('.zoneR1Wrapper ul').each(function () {
		var title = $(this).find('li').eq(0).find('a');
		var date = $(this).find('li').eq(1).text();

		zoneR1String += `<li><a href="${title.attr('href')}">${title.text()} <small>${date}</small> </a></li>`;
	});

	if($('.zoneR1Wrapper ul').length){
		$('.zoneR1Holder').replaceWith(`<h4 class="HeaderTextSmall">${$('.zoneR1Wrapper > h2').text()}</h4><ul>${zoneR1String}</ul>`);
	}

	
	$(".Highlight >p >a").unwrap();	
	$(".Highlight a").addClass("WhiteBorder btn-sm");
	$(".zoneK1Holder li").eq(0).find('a').addClass('active');

	$(".footCol1 h4").addClass("HeaderTextSmall");
	$(".footCol1 a").addClass("WhiteBorder btn-sm");
	$(".footCol1 a").unwrap();
	$(".footCol2 h4").addClass("HeaderTextSmall");
	$(".footCol4 h4").addClass("HeaderTextSmall");
	$(".footCol4 i").wrap("<span></span>");
   
   $('.homeSlider').owlCarousel({
	    loop:true,
	    items:1,
	    margin:0,
	    nav:false,
	    animateOut: 'fadeOut',
	    autoHeight:true,
	    dots:false,
	    autoplay: ($('.homeSlider > .item').length > 1)?true:false,
	    autoplayTimeout:7000,
	    autoplayHoverPause:true
	});

	// Sponsor slider
	  $('.sponsorSlider2').owlCarousel({ 
	    loop:true,
	  	dots:false,
	    drag:false,
	  	mouseDrag:false,
	  	autoplay: false,
        autoPlaySpeed: 7000,
        smartSpeed: 4000,
	    margin:0,
	    nav: false,
	    items:1,    
	    navText: ["<i class='fa fa-chevron-left' aria-hidden='true'></i>","<i class='fa fa-chevron-right' aria-hidden='true'></i>"]
	  });

	  $('.sponsorSlider1').owlCarousel({   
	    navText: ["<i class='fa fa-chevron-left' aria-hidden='true'></i>","<i class='fa fa-chevron-right' aria-hidden='true'></i>"],
		autoplay: false,
		loop:true,
	    responsive:{
	        0:{
	            items:2,margin:40, center:true
	        },
	        767:{
	            items:2,margin:40, center:true
	        }
	    }
	  }); 



	  // Add class to "search-btn-wrap" when clicking on "search-btn"
	$(".search-btn").click(function() {
		$(".header").toggleClass("search-active");
	});


	$(document).on('click', '[data-submenu]', function(e) {
		e.preventDefault();
		$('#'+ $(this).attr('data-submenu')).slideToggle();
	})
	 
	  $(document).on('click', '.list-inline ul li a', function() {
		if($(this).closest('li').find('ul').length) {
			$(this).closest('li').toggleClass('submenu-show')
		}
	  })
});

