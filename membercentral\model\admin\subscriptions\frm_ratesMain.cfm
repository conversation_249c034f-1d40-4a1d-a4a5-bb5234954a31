<cfoutput>
#local.strRateGLAcctWidget.js#
<div class="card card-box mt-2 ">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Rate Info</div>	
	</div>
	<div class="card-body">
		<div class="form-row">
			<div class="col-sm-6">
				<div class="form-group  ">
					<div class="form-label-group">
						<input type="text" name="rateName" id="rateName" value="#local.qryRate.rateName#" class="form-control" maxlength="100" >
						<label for="rateName">Rate Name *</label>
					</div>
				</div>
			</div>
			<div class="col-sm-3 px-sm-2">
				<select name="selRenewalRate" id="selRenewalRate" class="form-control ">
					<option value="0" <cfif local.isRenewalRate eq '0'>selected</cfif>>Join Rate</option>
					<option value="1"<cfif local.isRenewalRate eq '1'>selected</cfif>>Renewal Rate</option>
				</select>
			</div>
			<div class="col-sm-3">
				<select name="selStatus" id="selStatus" class="form-control ">
					<option value="A" <cfif local.rateStatus eq 'A'>selected</cfif>>Active</option>
					<option value="I"<cfif local.rateStatus eq 'I'>selected</cfif>>Inactive</option>
					<option value="D"<cfif local.rateStatus eq 'D'>selected</cfif>>Deleted</option>
				</select>
			</div>
		</div>
		<div class="form-group  ">
			<div class="form-label-group">
				<input type="text" name="reportCode" id="reportCode" value="#local.qryRate.reportCode#" class="form-control" maxlength="15" >
				<label for="reportCode" class="col-sm-3 col-form-label-sm font-size-md">Report Code</label>
			</div>
		</div>
		<cfif local.qryRate.RateID gt 0 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" name="rateUID" id="rateUID" value="#local.qryRate.uid#" class="form-control" maxlength="60">
					<label for="rateUID" class="col-sm-3 col-form-label-sm font-size-md">API ID</label>
				</div>
			</div>
		<cfelseif local.qryRate.RateID gt 0>
			<div class="form-group">
				<div class="form-label-group">
					<input type="text" name="rateUIDRO" id="rateUIDRO" value="#local.qryRate.uid#" class="form-control" maxlength="60" readonly="true">
					<label for="rateUIDRO" class="col-sm-3 col-form-label-sm font-size-md">API ID</label>
				</div>
			</div>
		</cfif>
		#local.strRateGLAcctWidget.html#
	</div>
</div>

<div class="card card-box mt-2 ">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Important Dates</div>	
	</div>
	<div class="card-body">
		<div class="form-group row no-gutters">
			<label for="rateStartDate" class="col-sm-4 col-form-label-sm font-size-md">Available *</label>
			<div class="col-sm-8 d-flex align-items-center">
				<div class="input-group input-group-sm">
					<input type="text" name="rateStartDate" id="rateStartDate" value="#local.frmRateStartDate#" class="form-control form-control-sm dateControl"/>
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="rateStartDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<span class="mx-sm-1 text-nowrap font-size-sm">12AM to</span>
				<div class="input-group input-group-sm">
					<input type="text" name="rateEndDate" id="rateEndDate" value="#local.frmRateEndDate#" class="form-control form-control-sm dateControl"/>
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="rateEndDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<span class="ml-sm-1 text-nowrap font-size-sm">11:59PM</span>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="termStartDate" class="col-sm-4 col-form-label-sm font-size-md">Term *</label>
			<div class="col-sm-8 d-flex align-items-center">
				<div class="input-group input-group-sm">
					<input type="text" name="termStartDate" id="termStartDate" value="#local.frmTermStartDate#" class="form-control form-control-sm dateControl"/>
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="termStartDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<span class="mx-sm-1 text-nowrap font-size-sm">12AM to</span>
				<div class="input-group input-group-sm">
					<input type="text" name="termEndDate" id="termEndDate" value="#local.frmTermEndDate#" class="form-control form-control-sm dateControl"/>
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="termEndDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<span class="ml-sm-1 text-nowrap font-size-sm">11:59PM</span>
			</div>
		</div>
		<div class="form-group row no-gutters">
			<label for="graceEndDate" class="col-sm-4 col-form-label-sm font-size-md">Grace End</label>
			<div class="col-sm-4 d-flex align-items-center">
				<div class="input-group input-group-sm">
					<input type="text" name="graceEndDate" id="graceEndDate" value="#local.frmGraceEndDate#" class="form-control form-control-sm dateControl"/>
					<div class="input-group-append">
						<span class="input-group-text cursor-pointer calendar-button" data-target="graceEndDate"><i class="fa-solid fa-calendar"></i></span>
					</div>
				</div>
				<span class="ml-sm-1 text-nowrap font-size-sm">11:59PM</span>
			</div>
			<div class="col-sm-4 d-flex align-items-center">
				<a href="javascript:mca_clearDateRangeField('graceEndDate');" class="btn btn-sm btn-link">Clear grace period</a>
			</div>
		</div>
		<cfif arguments.event.getValue('mc_siteInfo.useAccrualAcct')>
			<div class="form-group row no-gutters">
				<label for="recogStartDate" class="col-sm-4 col-form-label-sm font-size-md">Rev Recognition *</label>
				<div class="col-sm-8 d-flex align-items-center">
					<div class="input-group input-group-sm">
						<input type="text" name="recogStartDate" id="recogStartDate" value="#local.frmRecogStartDate#" class="form-control form-control-sm dateControl"/>
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="recogStartDate"><i class="fa-solid fa-calendar"></i></span>
						</div>
					</div>
					<span class="mx-sm-1 text-nowrap font-size-sm">12AM to</span>
					<div class="input-group input-group-sm">
						<input type="text" name="recogEndDate" id="recogEndDate" value="#local.frmRecogEndDate#" class="form-control form-control-sm dateControl"/>
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="recogEndDate"><i class="fa-solid fa-calendar"></i></span>
						</div>
					</div>
					<span class="ml-sm-1 text-nowrap font-size-sm">11:59PM</span>
				</div>
			</div>
		</cfif>
		<div class="form-group">
			<div class="form-label-group">
				<select name="selForceUpfront" id="selForceUpfront" class="form-control ">
					<option value="0" <cfif local.forceUpfront eq '0'>selected</cfif>>No</option>
					<option value="1"<cfif local.forceUpfront eq '1'>selected</cfif>>Yes</option>
				</select>
				<label for="selForceUpfront" class="col-sm-4 col-form-label-sm font-size-md">Must Pay on 1st invoice</label>
			</div>
		</div>	
		<div class="form-group">
			<div class="form-label-group">
				<select name="selFrontEndAllowChangePrice" id="selFrontEndAllowChangePrice" class="form-control" onchange="chkFrontEndAllowChangePriceOptions()">
					<option value="0" <cfif local.frontEndAllowChangePrice eq '0'>selected</cfif>>No</option>
					<option value="1"<cfif local.frontEndAllowChangePrice eq '1'>selected</cfif>>Yes</option>
				</select>
				<label for="selFrontEndAllowChangePrice" class="col-sm-4 col-form-label-sm font-size-md">User can change price</label>
			</div>
		</div>	
		<div id="FrontEndAllowChangePriceOptions" <cfif local.frontEndAllowChangePrice neq 1>class="d-none"</cfif>>
			<div class="form-group">
				<div class="form-label-group">
					<select name="selKeepChangedPriceOnRenewal" id="selKeepChangedPriceOnRenewal" class="form-control">
						<option value="0" <cfif local.keepChangedPriceOnRenewal eq '0'>selected</cfif>>No</option>
						<option value="1"<cfif local.keepChangedPriceOnRenewal eq '1'>selected</cfif>>Yes</option>
					</select>
					<label for="selKeepChangedPriceOnRenewal" class="col-sm-4 col-form-label-sm font-size-md">Renewal Keeps changed price</label>
				</div>
			</div>	
			<div class="form-group row no-gutters">
				<label for="selFrontEndChangePriceMin" class="col-sm-4 col-form-label-sm font-size-md">Range (#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#)</label>
				<div class="col-sm-8 d-flex">
					<input type="text" name="selFrontEndChangePriceMin" id="selFrontEndChangePriceMin" value="<cfif len(local.frontEndChangePriceMin)>#DecimalFormat(local.frontEndChangePriceMin)#</cfif>" class="form-control form-control-sm"/>
					<span class="mx-2">-</span>
					<input type="text" name="selFrontEndChangePriceMax" id="selFrontEndChangePriceMax" value="<cfif len(local.frontEndChangePriceMax)>#DecimalFormat(local.frontEndChangePriceMax)#</cfif>" class="form-control form-control-sm"/>
				</div>
			</div>
		</div>

		<cfif not local.qryOtherRenewalRates.recordcount><input type="hidden" name="selFallbackRenewalRateID" value=""></cfif>
		<cfif not local.qryOtherJoinRates.recordcount><input type="hidden" name="selLinkedNonRenewalRateID" value=""></cfif>
	</div>
</div>	

<cfif local.qryOtherRenewalRates.recordcount or local.qryOtherJoinRates.recordcount>
	<div class="card card-box mt-2 ">
		<div class="card-header bg-light">
			<div class="card-header--title font-weight-bold font-size-md">Join/Renew Rates Association</div>	
		</div>
		<div class="card-body">
			<cfif local.qryOtherRenewalRates.recordcount>
				<div class="form-group">
					<div class="form-label-group">
						<select name="selFallbackRenewalRateID" id="selFallbackRenewalRateID"  class="form-control">
							<option value=""></option>
							<cfloop query="local.qryOtherRenewalRates">
								<option value="#local.qryOtherRenewalRates.rateID#" <cfif local.qryOtherRenewalRates.rateID eq local.FallbackRenewalRateID>selected</cfif>>#local.qryOtherRenewalRates.rateName#</option>
							</cfloop>
						</select>
						<label for="selFallbackRenewalRateID" class="col-sm-4 col-form-label-sm font-size-md">Preferred Fallback Renewal Rate</label>
					</div>
				</div>	
			</cfif>
			<cfif local.qryOtherJoinRates.recordcount>
				<div class="form-group">
					<div class="form-label-group">
						<select name="selLinkedNonRenewalRateID" id="selLinkedNonRenewalRateID" class="form-control">						
							<option value=""></option>
							<cfloop query="local.qryOtherJoinRates">
								<option value="#local.qryOtherJoinRates.rateID#" <cfif local.qryOtherJoinRates.rateID eq local.LinkedNonRenewalRateID>selected</cfif>>#local.qryOtherJoinRates.rateName#</option>
							</cfloop>
						</select>
						<label for="selLinkedNonRenewalRateID" class="col-sm-4 col-form-label-sm font-size-md">Associated Join Rate</label>
					</div>
				</div>	
			</cfif>

		</div>
	</div>	
</cfif>
<div class="card card-box mt-2 ">
	<div class="card-header bg-light">
		<div class="card-header--title font-weight-bold font-size-md">Rate Frequencies</div>	
	</div>
	<div class="card-body">
		<cfloop query="local.qryRateFreqs">
			<cfset local.currRate = "">
			<cfif len(local.qryRateFreqs.rateAmt)>
				<cfset local.currRate = DollarFormat(local.qryRateFreqs.rateAmt)>
			</cfif>
			
			<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryCurrRFMPs">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				select rfmp.profileID
				from dbo.sub_rateFrequenciesMerchantProfiles as rfmp
				inner join dbo.sub_rateFrequencies as rf on rf.rfid = rfmp.rfid
					and rf.rateID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.rateID#"> 
					and rf.frequencyID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryRateFreqs.frequencyID#">
				where rfmp.status <> 'D';

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<div class="rateFrequencySection mt-2 border">
				<div class="row no-gutters p-2 bg-grey">
					<cfif local.qryRateFreqs.hasInstallments and isNumeric(local.qryRateFreqs.frequency) and isNumeric(local.qryRateFreqs.monthlyInterval)> 
						<cfset local.frtitleText="#local.qryRateFreqs.frequency# payment(s), #local.qryRateFreqs.monthlyInterval# month(s) apart"/>
					<cfelse>
						<cfset local.frtitleText="Single payment"/>
					</cfif>

					<div class="col-2">
						<label title="#local.frtitleText#" for="freqAmt_#local.qryRateFreqs.frequencyID#" class="font-weight-bold">
							#local.qryRateFreqs.frequencyName# <cfif local.qryRateFreqs.rateRequired eq 1>*</cfif> :
						</label>
					</div>
					<div class="col-10 form-inline">
						<div class="form-group mr-2">
							<input type="text" name="freqAmt_#local.qryRateFreqs.frequencyID#" id="freqAmt_#local.qryRateFreqs.frequencyID#" data-freqid="#local.qryRateFreqs.frequencyID#" value="#local.currRate#" class="form-control form-control-sm freqAmt" style="width:130px;" onkeyup="chkFreqAmt(#local.qryRateFreqs.frequencyID#);" onblur="this.value = formatCurrency(this.value);">
							<span class="ml-2">#arguments.event.getValue('mc_siteinfo.defaultCurrencyType')#</span>
						</div>
						<div class="form-group">
							<div class="form-check-inline">
								<input class="form-check-input freqAmtBlank" type="checkbox" name="freqAmt_#local.qryRateFreqs.frequencyID#_blank" id="freqAmt_#local.qryRateFreqs.frequencyID#_blank" value="1" onclick="chkFreqBlank(#local.qryRateFreqs.frequencyID#);" <cfif len(local.qryRateFreqs.rateAmt) eq 0>checked</cfif>>
								<label class="form-check-label text-nowrap" for="freqAmt_#local.qryRateFreqs.frequencyID#_blank">Rate Not Available</label>
							</div>
							<div class="form-check-inline">
								<input class="form-check-input" type="checkbox" name="freqFront_#local.qryRateFreqs.frequencyID#_allow" id="freqFront_#local.qryRateFreqs.frequencyID#_allow" value="1" <cfif local.qryRateFreqs.allowFrontEnd eq 1>checked</cfif>>
								<label class="form-check-label text-nowrap" for="freqFront_#local.qryRateFreqs.frequencyID#_allow">Allow on Front End</label>
							</div>
						</div>
					</div>
				</div>
				<div class="row no-gutters mb-3 p-2 divRFMP <cfif not len(local.qryRateFreqs.rateAmt)>d-none</cfif>" id="divRFMP_#local.qryRateFreqs.frequencyID#">
					<div class="col">
						<div class="freqAmtMsg text-danger font-weight-bold py-1">There are no merchant profiles assigned to #local.qryRateFreqs.frequencyName#.</div>
						#local.objPaymentMethodSelector.getPaymentProfileSelector(siteID=arguments.event.getValue('mc_siteInfo.siteID'), selectorID="addRFMP_#local.qryRateFreqs.frequencyID#", selectedPaymentProfileIDList=valueList(local.qryCurrRFMPs.profileID), title="Allowed payment methods for #local.qryRateFreqs.frequencyName# *", colCount=2, allowOffline=true, allowPayLater=true, onChangeFuncName='validateProfileSelected').html#
					</div>
				</div>
			</div>
		</cfloop>
	</div>
</div>	
</cfoutput>