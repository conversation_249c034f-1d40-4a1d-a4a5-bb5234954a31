<cfsavecontent variable="local.editSubscriptionJS">
<cfoutput>
	#local.strSubscriptionGLAcctWidget.js#
	<script language="javascript">
		function validateSubscriptionForm() {
			var arrReq = new Array();
			hideAlert();

			var selType = $('##selType').val() || '';
			if (selType == '') arrReq[arrReq.length] = 'Please select a Subscription Type.';

			if ($('##subName').val() == '')
				arrReq[arrReq.length] = 'You must enter a valid name for this Subscription.';
			
			<cfif local.subData.subscriptionID gt 0 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
				if ($('##subUID').val() == '')
					arrReq[arrReq.length] = 'You must enter a valid UID.';
			</cfif>

			var selSched = $('##selSched').val() || '';
			if (selSched == '') arrReq[arrReq.length] = 'Please select a Rate Schedule.';

			var selActOpt = $('##selActOpt').val() || '';
			if (selActOpt == '') arrReq[arrReq.length] = 'Please select an Activation Option.';

			var selAltActOpt = $('##selAltActOpt').val() || '';
			if (selAltActOpt == '') arrReq[arrReq.length] = 'Please select an Alternate Activation Option.';
				
			var glAccountID = $('##SubscriptionGLAccountID').val() || '';
			if (glAccountID.length == 0 || glAccountID == 0)
				arrReq[arrReq.length] = 'Please select a GL Account for the Subscription.';

			var payOrder = $('##payOrder').val();
			if(payOrder == '' || !(mca_validateInteger(payOrder))) arrReq[arrReq.length] = "You must enter a valid Payment Order.";
			else if (payOrder == 0) arrReq[arrReq.length] = 'Please enter a valid Payment Order (must be greater than 0).';
			
			if ($('##selSoldSep').val() == 1 && $('##selAltActOpt').val() == 'F')
				arrReq[arrReq.length] = 'Subscriptions that are sold separately cannot have an Alternate Activation Option of Follow Parent. Please select a valid Alternate Activation Option.';

			if(arrReq.length){
				showAlert(arrReq.join('<br/>'));
			}
			else {
				chkSubNameInUse();
				toggleSubmitButton(false);
			}
			
			return false;
		}
		function chkSubNameInUse() {
			var chkSubNameResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					if (r.subnameinuse == true) {
						toggleSubmitButton(true);
						showAlert('Entered Subscription Name is already in use.<br/>Please enter a valid Subscription Name.');
					}
					else {
						$('##frmSub')[0].submit();
					}
				} 
				else{
					toggleSubmitButton(true);
					showAlert('Unable to save Subscription.');
				}
			};
			var objParams = { subID: $('##subID').val(), subName:$('##subName').val()};
			TS_AJX('ADMSUBS','checkSubName',objParams,chkSubNameResult,chkSubNameResult,20000,chkSubNameResult);
		}
		function selectEmailTemplate(retF) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Select Email Template',
				iframe: true,
				contenturl: '#this.link.startGenerateOffers#&templateOnly=1&retF=' + retF,
				strmodalfooter: {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##btnSelectTemplate").click',
					extrabuttonlabel: 'Select Template'
				}
			});
		}
		function selectEmailTemplateResult(objET) {
			selectEmailTemplateResultMain(objET,'SubscriptionEmailTemplate','Welcome');
		}
		function selectRenewEmailTemplateResult(objET) {
			selectEmailTemplateResultMain(objET,'SubscriptionRenewEmailTemplate','Renew');
		}
		function selectEmailTemplateResultMain(objET,fldName,descName) {
			if (objET.templateName.length > 0) {
				var tmpFldName = '##' + fldName;
				var tmpFldID = tmpFldName + 'ID';
				var divName = fldName + 'Remove';
				$(tmpFldName).html('<span class="mr-2">' + objET.templateName + '</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
				$(tmpFldID).val(objET.templateID);
				$('##' + divName).show();
				MCModalUtils.hideModal();
			} else {
				var msg = '<div style="margin:10px;">
					<h4>Error selecting Email Template</h4>
					<div>There was a problem selecting the ' + descName + 'Email Template.<br/>
					Try again; if the issue persists, contact MemberCentral for assistance.</div>
					</div>';
				$('##MCModalBody').html(msg);
			}
		}
		function removeEmailTemplate() {
			removeEmailTemplateMain('SubscriptionEmailTemplate');
		}
		function removeRenewEmailTemplate() {
			removeEmailTemplateMain('SubscriptionRenewEmailTemplate');
		}
		function removeEmailTemplateMain(fldName) {
			var tmpFldName = '##' + fldName;
			var tmpFldID = tmpFldName + 'ID';
			var divName = fldName + 'Remove';
			$(tmpFldName).html('<span class="mr-2">(no template selected)</span> (<span class="text-danger font-weight-bold">Remember to save!</span>)');
			$(tmpFldID).val(0);
			$('##' + divName).hide();
		}
		function changeSS() {
			if ($('##selSoldSep').val() == "1") $('##etSectionDiv').show();
			else $('##etSectionDiv').hide();
		}
		function closeBox() { MCModalUtils.hideModal(); }
		function hideAlert() { $('##err_sub').html('').addClass('d-none'); };
		function showAlert(msg) { $('##err_sub').html(msg).removeClass('d-none'); };
		function toggleSubmitButton(f){ $('##btnSaveSubscription').attr('disabled', !f); }

		$(function() {
			mca_setupCustomFileControls('frmSub');
			changeSS();
		});
	</script>
</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.editSubscriptionJS)#">

<cfoutput>
<form method="POST" name="frmSub" id="frmSub" action="#local.formLink#" enctype="multipart/form-data" onsubmit="return validateSubscriptionForm();">
<input type="hidden" name="subID" id="subID" value="#local.subData.subscriptionID#">
<input type="hidden" name="frontEndContentID" id="frontEndContentID" value="#local.subData.frontEndContentID#">
<input type="hidden" name="frontEndCompletedContentID" id="frontEndCompletedContentID" value="#local.subData.frontEndCompletedContentID#">

<div class="col-sm-12 p-1">
<div class="row">
	<cfif local.subData.subscriptionID neq 0>
		<div class="col-auto font-italic">Note: Changes made to this subscription will not affect current subscribers.</div>
	</cfif>
	<div class="col text-right">
		<button type="submit" name="btnSaveSubscription" id="btnSaveSubscription" class="btn btn-sm btn-primary">Save Subscription</button>
	</div>
</div>

<div id="err_sub" class="alert alert-danger my-2 d-none"></div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selType" id="selType" class="form-control">
			<cfloop query="local.qrySubTypes">
				<option value="#local.qrySubTypes.typeID#" <cfif local.qrySubTypes.typeID eq local.subData.typeID>selected</cfif>>#local.qrySubTypes.typeName#</option>
			</cfloop>
		</select>
		<label for="selType">Subscription Type *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<input type="text" name="subName" id="subName" value="#local.subData.subscriptionName#" class="form-control" maxlength="100">
		<label for="subName">Name of Subscription *</label>
	</div>
</div>
<div class="form-group my-3">
	<div class="form-label-group mb-1">
		<input type="text" name="reportCode" id="reportCode" value="#local.subData.reportCode#" class="form-control" maxlength="15">
		<label for="reportCode">Report Code</label>
	</div>
	<div class="font-size-sm">(used to label this subscription in reports)</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selStatus" id="selStatus" class="form-control">
			<option value="A" <cfif local.subData.status eq 'A'>selected</cfif>> Active</option>
			<option value="I" <cfif local.subData.status eq 'I'>selected</cfif>> Inactive</option>
		</select>
		<label for="selStatus">Status *</label>
	</div>
</div>
<cfif local.subData.subscriptionID gt 0 AND application.objUser.isSuperUser(cfcuser=session.cfcuser)>
	<div class="form-group mt-3">
		<div class="form-label-group">
			<input type="text" name="subUID" id="subUID" value="#local.subData.uid#" class="form-control" maxlength="60">
			<label for="subUID">API ID *</label>
		</div>
	</div>
<cfelseif local.subData.subscriptionID gt 0>
	<div class="form-group mt-3">
		<div class="form-label-group">
			<input type="text" name="subUIDRO" id="subUIDRO" value="#local.subData.uid#" class="form-control" maxlength="60" readonly="true">
			<label for="subUIDRO">API ID</label>
		</div>
	</div>
</cfif>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selSched" id="selSched" class="form-control">
			<cfloop query="local.qryRateSched">
				<option value="#local.qryRateSched.scheduleID#" <cfif local.qryRateSched.scheduleID eq local.subData.scheduleID>selected</cfif>>#local.qryRateSched.scheduleName#</option>
			</cfloop>
		</select>
		<label for="selSched">Rate Schedule *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selTermDates" id="selTermDates" class="form-control">
			<option value="A" <cfif local.subData.rateTermDateFlag eq 'A'>selected</cfif>>Adhere to term dates in rate
			<option value="S" <cfif local.subData.rateTermDateFlag eq 'S'>selected</cfif>>Use current day as term start date (adhere to term end date in rate)
			<option value="C" <cfif local.subData.rateTermDateFlag eq 'C'>selected</cfif>>Calculate term end date for term length (term start date is current day)
		</select>
		<label for="selTermDates">Term Dates *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selAutoExp" id="selAutoExp" class="form-control">
			<option value="0" <cfif local.subData.autoExpire eq 0>selected</cfif>> No
			<option value="1"<cfif local.subData.autoExpire eq 1>selected</cfif>> Yes
		</select>
		<label for="selAutoExp">Auto Expire Subscribers *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selSoldSep" id="selSoldSep" class="form-control" onchange="changeSS();">
			<option value="0" <cfif local.subData.soldSeparately eq 0>selected</cfif>> No
			<option value="1" <cfif local.subData.soldSeparately eq 1>selected</cfif>> Yes
		</select>
		<label for="selSoldSep">Sold Separately *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selActOpt" id="selActOpt" class="form-control">
			<cfloop query="local.qryActivationOptions">
				<option value="#local.qryActivationOptions.subActivationCode#" <cfif local.qryActivationOptions.subActivationCode eq local.subData.subActivationCode>selected</cfif>>#local.qryActivationOptions.subActivationName#</option>
			</cfloop>
		</select>
		<label for="selActOpt">Activation Option *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selAltActOpt" id="selAltActOpt" class="form-control">
			<cfloop query="local.qryActivationOptions">
				<option value="#local.qryActivationOptions.subActivationCode#" <cfif local.qryActivationOptions.subActivationCode eq local.subData.subAlternateActivationCode>selected</cfif>>#local.qryActivationOptions.subActivationName#</option>
			</cfloop>
		</select>
		<label for="selAltActOpt">Alternate Activation Option *</label>
	</div>
</div>
#local.strSubscriptionGLAcctWidget.html#
<div class="form-group mt-3">
	<div class="form-label-group">
		<select name="selRateGLOverride" id="selRateGLOverride" class="form-control">
			<option value="0" <cfif local.subData.allowRateGLAccountOverride eq 0>selected</cfif>> No
			<option value="1" <cfif local.subData.allowRateGLAccountOverride eq 1>selected</cfif>> Yes
		</select>
		<label for="selRateGLOverride">Allow Rate GL Account Override *</label>
	</div>
</div>
<div class="form-group mt-3">
	<div class="form-label-group">
		<input type="text" name="payOrder" id="payOrder" value="#local.subData.paymentOrder#" class="form-control" maxlength="5">
		<label for="payOrder">Payment Order *</label>
	</div>
</div>
<div class="card" id="etSectionDiv">
	<div class="card-body p-3">
		<div class="form-group row">
			<div class="col-sm-3" id="etTitle">Welcome Email Template</div>
			<div class="col-sm-9">
				<span id="etValue">
					<input type="hidden" name="SubscriptionEmailTemplateID" id="SubscriptionEmailTemplateID" value="#local.subData.emailTemplateID#" />
					<span id="SubscriptionEmailTemplate"><cfif len(local.subData.emailTemplateName)>#local.subData.emailTemplateName#<cfelse>(no template selected)</cfif></span>
					<br/><a href="javascript:selectEmailTemplate('selectEmailTemplateResult');">Choose Email Template</a><span id="SubscriptionEmailTemplateRemove" <cfif val(local.subData.emailTemplateID) eq 0>style="display:none;"</cfif>>&nbsp;&nbsp;&nbsp;<a href="javascript:removeEmailTemplate();">Remove Email Template</a></span>
				</span>
			</div>
		</div>
	</div>
</div>
<div class="card">
	<div class="card-body p-3">
		<div class="form-group row">
			<div class="col-sm-3" id="retTitle">Renew Email Template</div>
			<div class="col-sm-9">
				<span id="retValue">
					<input type="hidden" name="SubscriptionRenewEmailTemplateID" id="SubscriptionRenewEmailTemplateID" value="#local.subData.renewEmailTemplateID#" />
					<span id="SubscriptionRenewEmailTemplate"><cfif len(local.subData.renewEmailTemplateName)>#local.subData.renewEmailTemplateName#<cfelse>(no template selected)</cfif></span>
					<br/><a href="javascript:selectEmailTemplate('selectRenewEmailTemplateResult');">Choose Email Template</a><span id="SubscriptionRnewEmailTemplateRemove" <cfif val(local.subData.renewEmailTemplateID) eq 0>style="display:none;"</cfif>>&nbsp;&nbsp;&nbsp;<a href="javascript:removeRenewEmailTemplate();">Remove Email Template</a></span>
				</span>
			</div>
		</div>
	</div>
</div>
<div class="form-group mt-3">
	#application.objWebEditor.showContentBoxWithLinks(fieldname='frontEndContent', fieldlabel='Front End Content:', contentID=local.subData.frontEndContentID, content=local.subData.feContentRawContent, allowMergeCodes=1, mergeCodeList="incSub", supportsBootstrap=true, allowVersioning=true)#
</div>
<div class="form-group mt-3">
	#application.objWebEditor.showContentBoxWithLinks(fieldname='frontEndParentSubContent', fieldlabel='Front End Parent Subscription Content:', contentID=local.subData.frontEndParentSubContentID, content=local.subData.frontEndParentSubRawContent, allowMergeCodes=1, mergeCodeList="incSub", supportsBootstrap=true, allowVersioning=true)#
</div>
<div class="form-group mt-3">
	#application.objWebEditor.showContentBoxWithLinks(fieldname='frontEndCompletedContent', fieldlabel='Front End Completed Content:', contentID=local.subData.frontEndCompletedContentID, content=local.subData.feCompletedContentRawContent, allowMergeCodes=1, mergeCodeList="incSub", supportsBootstrap=true, allowVersioning=true)#
</div>
</div>
</form>
</cfoutput>