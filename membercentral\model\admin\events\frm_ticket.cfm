<cfsavecontent variable="local.ticketJS">
	<cfoutput>
	#local.strRevenueGLAcctWidget.js#
	<script language="javascript">
		function validateTicketForm() {
			mca_hideAlert('ev_ticket_err_div');
			$('##frmTicket :submit').prop('disabled',true);
			var arrReq = [];
			
			if ($('##frmTicket ##ticketName').val().trim() == ''){
				arrReq[arrReq.length] = 'Enter the name of this ticket.<br/>';				
			}

			if ($('##frmTicket ##ticketName').val() != ''){
				var checkDuplicateTicketResult = function(r) {
					if (r.success && r.success.toLowerCase() == 'true'){ 
						$('##frmTicket').submit();
					}
					else {
						arrReq[arrReq.length] = 'This Ticket Name is already in use for this Event. Provide a unique Ticket Name to correct the issue.<br/>';	
						$('##ev_ticket_err_div').html(arrReq.join('')).addClass('alert alert-danger').show();
						$('##ev_ticket_err_div').removeClass('d-none');
						$('##frmTicket :submit').prop('disabled',false);
						$('html, body').animate({
							scrollTop: $('##ev_ticket_err_div').offset().top
						}, 1000);
						return false;
					}
				};


				var objParams = {
					eID:$('##frmTicket ##eID').val(),	
					ticketID:$('##frmTicket ##ticketid').val(),					
					registrationID:$('##frmTicket ##registrationID').val(),
					strType:'ticket',
				 	ticketName:$('##frmTicket ##ticketName').val().trim() };
				TS_AJX('ADMINEVENT','validateTicketAndPackageName',objParams,checkDuplicateTicketResult,checkDuplicateTicketResult,10000,checkDuplicateTicketResult);

			}else{
				mca_showAlert('ev_ticket_err_div', 'Enter the name of this ticket');
				$('##frmTicket :submit').prop('disabled',false);
				$('html, body').animate({
					scrollTop: $('##ev_ticket_err_div').offset().top
				}, 1000);
				return false;
			}

			return false;
		}

		$(function() {
			$(document).on('click','.submitBtn',function(){
				validateTicketForm();
			});
		});

	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.ticketJS)#">

<cfoutput>
<div id="divTicketForm">
	<form name="frmTicket" id="frmTicket" class="p-2" action="#local.formlink#" method="post">
	<input type="hidden" name="registrationID" id="registrationID" value="#arguments.event.getValue('registrationID')#">
	<input type="hidden" name="ticketid" id="ticketid" value="#arguments.event.getValue('ticketid')#">
	<input type="hidden" name="eID" id="eID" value="#arguments.event.getValue('eID')#">
	
	<div id="ev_ticket_err_div" class="alert alert-danger mb-2 d-none"></div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<input type="text" name="ticketName" id="ticketName" value="#arguments.event.getValue('ticketName')#" class="form-control" maxlength="100" />
				<label for="ticketName">Name of Ticket *</label>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<textarea name="ticketDescription" id="ticketDescription" rows="2" class="form-control">#arguments.event.getValue('ticketDescription')#</textarea>
				<label for="ticketDescription">Description</label>
			</div>
		</div>
	</div>
	
	<cfif variables.enableEventGuestTracking>
		<div class="form-row">
			<div class="col">
				<div class="form-label-group">
					<select name="assignToMembers" id="assignToMembers" class="form-control">
						<option value="0" <cfif arguments.event.getValue('assignToMembers') is not 1>selected</cfif>>No, this ticket will NOT track guests.</option>
						<option value="1" <cfif arguments.event.getValue('assignToMembers') is 1>selected</cfif>>Yes, this ticket will track guests.</option>
					</select>
					<label for="assignToMembers">Track Guests *</label>
				</div>
			</div>
		</div>
	<cfelse>
		<input type="hidden" name="assignToMembers" id="assignToMembers" value="0">
	</cfif>
	
	<div class="form-row mb-3">
		<div class="col">
			<div class="row no-gutters align-items-center">
				<div class="col-md col-sm-12">
					<div class="form-label-group m-0">
						<input type="text" name="inventory" id="inventory" value="#arguments.event.getValue('inventory')#" class="form-control" maxlength="8" />
						<label for="inventory">Max Tickets Available</label>
					</div>
				</div>
				<div class="ml-1 col-md-auto align-items-center">(leave blank if there is no limit)</div>
			</div>
		</div>
	</div>
	
	<div class="form-row">
		<div class="col">
			<div class="form-label-group">
				<select name="autoManageInventory" id="autoManageInventory" class="form-control">
					<option value="0" <cfif arguments.event.getValue('autoManageInventory') is not 1>selected</cfif>>Do NOT automatically remove ticket packages that exceed the Max Tickets Available.</option>
					<option value="1" <cfif arguments.event.getValue('autoManageInventory') is 1>selected</cfif>>Automatically remove ticket packages that exceed the Max Tickets Available.</option>
				</select>
				<label for="autoManageInventory">Exceeding Package Options *</label>
			</div>
			<small class="form-text text-black-50">If enabled, ticket packages that would exceed the cap will not be available for purchase by registrant.</small>
		</div>
	</div>
	
	<div class="form-row mt-2">
		<div class="col">
			#local.strRevenueGLAcctWidget.html#
		</div>
	</div>
	<button type="button" class="d-none submitBtn"></button>
	<button type="submit" class="d-none submitBtnProcess"></button>
	</form>
</div>

</cfoutput>